"""
Configuration settings for the Weather Analysis CLI tool.
"""
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# OpenWeatherMap API configuration
API_KEY = os.getenv('OPENWEATHER_API_KEY', 'your_api_key_here')
BASE_URL = 'https://api.openweathermap.org/data/2.5/weather'

# File paths
CITIES_FILE = 'cities.txt'
DATA_FILE = 'weather_data.csv'
REPORT_FILE = 'weather_report.txt'

# API settings
TIMEOUT = 10  # seconds
UNITS = 'metric'  # for Celsius temperature

# Default cities if file is not found
DEFAULT_CITIES = [
    'Mumbai',
    'Delhi',
    'Bangalore',
    'Hyderabad',
    'Chennai',
    'Kolkata',
    'Pune',
    'Ahmedabad',
    'Jaipur',
    'Chandigarh'
]
