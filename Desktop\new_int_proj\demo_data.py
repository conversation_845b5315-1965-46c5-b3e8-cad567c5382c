#!/usr/bin/env python3
"""
Demo script to test the weather analysis tool with sample data
"""
import pandas as pd
from analyzer import WeatherAnalyzer
from report_generator import ReportGenerator
from data_handler import DataHandler

# Sample weather data that matches the expected format
sample_weather_data = [
    {
        'city': 'Mumbai',
        'temperature': 28.5,
        'humidity': 78,
        'description': 'Clear Sky',
        'wind_speed': 3.2,
        'country': 'IN',
        'timestamp': '2024-01-15 14:30:00'
    },
    {
        'city': 'Delhi',
        'temperature': 35.5,
        'humidity': 45,
        'description': 'Clear Sky',
        'wind_speed': 2.1,
        'country': 'IN',
        'timestamp': '2024-01-15 14:30:00'
    },
    {
        'city': 'Bangalore',
        'temperature': 24.0,
        'humidity': 65,
        'description': 'Clear Sky',
        'wind_speed': 1.8,
        'country': 'IN',
        'timestamp': '2024-01-15 14:30:00'
    },
    {
        'city': 'Shimla',
        'temperature': 7.0,
        'humidity': 85,
        'description': 'Light Rain',
        'wind_speed': 4.5,
        'country': 'IN',
        'timestamp': '2024-01-15 14:30:00'
    },
    {
        'city': 'Chandigarh',
        'temperature': 22.4,
        'humidity': 60,
        'description': 'Clear Sky',
        'wind_speed': 2.8,
        'country': 'IN',
        'timestamp': '2024-01-15 14:30:00'
    },
    {
        'city': 'Jaipur',
        'temperature': 31.2,
        'humidity': 42,
        'description': 'Clear Sky',
        'wind_speed': 3.1,
        'country': 'IN',
        'timestamp': '2024-01-15 14:30:00'
    },
    {
        'city': 'Pune',
        'temperature': 26.8,
        'humidity': 55,
        'description': 'Clear Sky',
        'wind_speed': 2.5,
        'country': 'IN',
        'timestamp': '2024-01-15 14:30:00'
    },
    {
        'city': 'Ahmedabad',
        'temperature': 29.3,
        'humidity': 68,
        'description': 'Light Rain',
        'wind_speed': 3.8,
        'country': 'IN',
        'timestamp': '2024-01-15 14:30:00'
    },
    {
        'city': 'Chennai',
        'temperature': 30.1,
        'humidity': 72,
        'description': 'Partly Cloudy',
        'wind_speed': 4.2,
        'country': 'IN',
        'timestamp': '2024-01-15 14:30:00'
    },
    {
        'city': 'Kolkata',
        'temperature': 27.6,
        'humidity': 80,
        'description': 'Overcast Clouds',
        'wind_speed': 2.9,
        'country': 'IN',
        'timestamp': '2024-01-15 14:30:00'
    }
]

def run_demo():
    """Run the demo with sample data"""
    print("🎬 Running Weather Analysis Demo with Sample Data")
    print("=" * 60)
    
    # Save sample data to CSV
    data_handler = DataHandler('demo_weather_data.csv')
    data_handler.save_to_csv(sample_weather_data)
    
    # Load data and analyze
    df = pd.DataFrame(sample_weather_data)
    analyzer = WeatherAnalyzer(df)
    analysis = analyzer.get_comprehensive_analysis()
    
    # Generate report
    report_generator = ReportGenerator('demo_weather_report.txt')
    report_content = report_generator.generate_report(analysis, sample_weather_data)
    
    # Save and display report
    report_generator.save_report(report_content)
    report_generator.print_report(report_content)
    
    print("\n🎉 Demo completed successfully!")
    print("Files created:")
    print("  • demo_weather_data.csv")
    print("  • demo_weather_report.txt")

if __name__ == "__main__":
    run_demo()
